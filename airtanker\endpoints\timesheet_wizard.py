from main import airtanker_app
from flask import render_template, request, jsonify, session, flash, redirect, url_for, make_response
import pandas as pd
import io

from endpoints.decorators import requires_authentication, requires_odoo_authentication

from services.EmployeeService import push_updates, push_updates_customer, update_travel_hours_for_not_errored_employees
from services.OdooService import OdooService
from services.ExcelFileService import ExcelFileService
from services.PaycorService import PaycorService
from services.GeminiService import GeminiService

from forms.WorkOrderForm import *
from services.DatabaseService import DatabaseService
from services.FileParserService import FileParserService
from datetime import datetime, timedelta


# Step 1 - Import Work Orders From Odoo
@airtanker_app.route('/import_work_orders', methods=['GET', 'POST'])
@requires_odoo_authentication
@requires_authentication
def import_work_orders():
    form = WorkOrderForm()

    if 'internal_errors_found' in session:
        session.pop('internal_errors_found', None)

    if request.method in ('GET'):
        database_service = DatabaseService()
        database_service.connect()
        last_import = "No Imports Yet."

        query = """
            SELECT TOP 1 [ImportDate]
            FROM [dbo].[WorkOrders]
            ORDER BY [ImportDate] DESC
        """
        results = database_service.execute_query(query)

        if results:
            last_import_date = results[0]["ImportDate"]
            adjusted_import_date = last_import_date - timedelta(hours=4)  # Subtract 4 hours            
            today_date = datetime.now().date()
            
            if adjusted_import_date.date() == today_date:
                last_import = f"Today at {adjusted_import_date.strftime('%I:%M %p')}"
            else:
                last_import = adjusted_import_date.strftime("%Y-%m-%d %I:%M %p")

        database_service.disconnect()
        return render_template('timesheet_wizard/import_work_orders.html', form=form, last_import=last_import)
    
    if form.validate_on_submit():  # This will automatically validate the CSRF token
        selected_week_ending = form.sundayPicker.data  # Access the selected date
        session['selected_week_ending'] = selected_week_ending
        odoo_service = session['odoo_service'] # type: OdooService
        odoo_service.import_work_orders_to_db(selected_week_ending)

        # Add step complete to session

        # redirect to step 2 - import internal sheets
        return redirect(url_for('upload_internal_timesheets') + f'?week_ending_date={selected_week_ending}')

# Step 2 - Upload Internal Sheets
@airtanker_app.route('/upload_internal_timesheets', methods=['GET', 'POST'])
@requires_odoo_authentication
@requires_authentication
def upload_internal_timesheets():
    if request.args.get("week_ending_date"):
        session['selected_week_ending'] = request.args.get("week_ending_date")

    if request.method in ('POST'):
        excel_file_service = ExcelFileService()

        excel_file_service.name_errors = []
        excel_file_service.error_logs = []

        uploaded_files = request.files.getlist('uploaded_files[]')        
        errors, name_errors, fileIds = excel_file_service.parse_internal_files(uploaded_files,
                                                                        session['selected_week_ending'],
                                                                        session["odoo_service"])
        if errors or name_errors:
            redirect_url = url_for('resolve_internal_errors')
            return jsonify({'redirect_url': redirect_url, "errors":errors, "name_errors":name_errors, "fileIds":fileIds})
        else:
            redirect_url = url_for('upload_customer_timesheets')
            return jsonify({'redirect_url': redirect_url})

    return render_template('timesheet_wizard/upload_internal_timesheets.html')

@airtanker_app.route('/import_paycor_timesheets', methods=['GET', 'POST'])
@requires_odoo_authentication
@requires_authentication
def import_paycor_timesheets():
    selected_week_ending = False
    if request.args.get("week_ending_date"):
        selected_week_ending = request.args.get("week_ending_date")
        import_type = "external"

    if request.form:
        selected_week_ending = request.form['week_ending_date']
        import_type = request.form['type'] 

    session['selected_week_ending'] = selected_week_ending
    session["import_type"] = import_type

    if request.method in ('POST'):        
        # Get Paycor API Service
        # paycor_service = PaycorService()
        # errors, name_errors, fileId = paycor_service.import_timecards(selected_week_ending, session['odoo_service'], import_type)

        # Use Paycor Excel import instead
        excel_service = ExcelFileService()        
        excel_service.name_errors = []
        excel_service.error_logs = []
        
        uploaded_files = request.files.getlist('uploaded_files[]')
        errors, name_errors, file_ids = excel_service.parse_fixed_files(uploaded_files, selected_week_ending, session['odoo_service'])

        # If errors need to be resolved, return them to UI
        if errors or name_errors:
            redirect_url = url_for('resolve_internal_errors')
            return jsonify({'redirect_url': redirect_url, "errors": errors, "name_errors": name_errors, "fileIds": file_ids})
        # Else, move on to customer timesheets
        elif import_type == "internal":
            return jsonify({'redirect_url': f'/export_fixed_timesheets?week_ending={selected_week_ending}'})
        else:
            redirect_url = url_for('upload_customer_timesheets')
            return jsonify({'redirect_url': redirect_url})
    
    return render_template('timesheet_wizard/upload_internal_timesheets.html')

# Step 2.5 - Resolve Internal Timesheet Conflicts
@airtanker_app.route('/resolve_internal_errors', methods=['GET', 'POST'])
@requires_odoo_authentication
@requires_authentication
def resolve_internal_errors():
    if request.method in ('POST'):
        # airtanker_app.logger.debug("Submitted CSRF token: %s", request.form.get('csrf_token'))
        # airtanker_app.logger.debug("Session CSRF token: %s", session.get('_csrf_token'))
        # update the records in the database
        data_received = request.json
        if data_received:
            push_updates(data_received, session['username'])

        if "import_type" in session and session["import_type"] and session["import_type"] == 'internal':
            selected_week_ending = session['selected_week_ending']
            redirect_url = f'/export_fixed_timesheets?week_ending={selected_week_ending}'
        else:
            redirect_url = url_for('upload_customer_timesheets')
        return jsonify({'redirect_url': redirect_url})           

    return render_template('timesheet_wizard/resolve_internal_errors.html')

# Step 3 - Upload Customer Timesheets
@airtanker_app.route('/upload_customer_timesheets', methods=['GET', 'POST'])
@requires_odoo_authentication
@requires_authentication
def upload_customer_timesheets():
    if request.method in ('POST') :
        # airtanker_app.logger.debug("Submitted CSRF token: %s", request.form.get('csrf_token'))
        # airtanker_app.logger.debug("Session CSRF token: %s", session.get('_csrf_token'))            
        excel_file_service = ExcelFileService()

        excel_file_service.name_errors = []
        excel_file_service.error_logs = []

        uploaded_files = request.files.getlist('uploaded_files[]')        
        errors, name_errors = excel_file_service.parse_external_files(uploaded_files, session['selected_week_ending'])
        if errors or name_errors:
            redirect_url = url_for('resolve_customer_errors')
            return jsonify({'redirect_url': redirect_url, "errors":errors, 'name_errors':name_errors})
        else:
            completion_html = render_template('timesheet_wizard/wizard_complete.html')

            # Return the HTML content in the JSON response
            return jsonify({'success': True, 'completion_html': completion_html})

    return render_template('timesheet_wizard/upload_customer_timesheets.html')

# Step 3 (alternative) - Upload Customer Timesheets with beta ai feature
@airtanker_app.route('/upload_customer_timesheets_ai', methods=['GET', 'POST'])
@requires_odoo_authentication
@requires_authentication
def upload_customer_timesheets_ai():
    if request.method == 'POST':
        # Grab all files and template IDs
        files = request.files.getlist('uploaded_files[]')
        template_ids = request.form.getlist('file_templates[]')

        print(f"Received {len(files)} files on /upload_customer_timesheets_ai")

        selected_week_ending = session['selected_week_ending']
        print(f"Selected week ending: {selected_week_ending}")

        # Initialize db connection so all services below use the same connection and disconnect with finally at the end
        db_service = DatabaseService()
        db_service.connect()

        try:
            # Returns: pd.DataFrame with columns ['Date', 'CustomerReportedHours', 'EmployeeName', 'CustomerName', 'FileID']
            parsed_files_df = FileParserService().parse_customer_timesheets_dummy(files=files, template_ids=template_ids, db_service=db_service)
            # For testing purposes use FileParserService().parse_customer_timesheets_dummy, this returns a hardcoded df
            print("Parsing done.")

            '''
            Data till this point:
                - FileID
                - Date
                - EmployeeName
                - CustomerReportedHours
                - CustomerName (optional* if CustomerID is provided)
                - CustomerID (optional) - lets make it CustomerName only TODO
                - Location (optional)
                - RateType (optional)
            '''

            # Returns: [{'WorkOrderID', 'EmployeeID', 'FullName', 'CustomerID', 'CustomerName', 'ProjectID', 'StageID', 'StartDate', 'AnticipatedEndDate*', ...}]
            # First get all active work orders for week ending
            all_active_work_orders = db_service.get_active_work_orders(selected_week_ending=selected_week_ending)
            print("First step done.")

            # Returns: pd.DataFrame with added columns ['CustomerID*', 'ErrorMessage*', 'PossibleCustomerIDs*']
            # Second, match customer names
            df_with_customer_ids = FileParserService().parse_customer_names(df=parsed_files_df, all_active_work_orders=all_active_work_orders, db_service=db_service)
            print("Second step done.")

            # Returns: pd.DataFrame with added columns ['EmployeeID*', 'ErrorMessage*', 'PossibleEmployeeIDs*']
            # Third, match employee names
            df_with_employee_ids = FileParserService().parse_employee_names(df=df_with_customer_ids, all_active_work_orders=all_active_work_orders, db_service=db_service)
            print("Third step done.")

            # Returns: pd.DataFrame with added column ['RateTypeID*']
            # Fourth, match RateType if exists
            df_with_rate_type_ids = FileParserService().parse_rate_types(df=df_with_employee_ids, db_service=db_service)
            print("Fourth step done.")

            # Returns: pd.DataFrame with added column ['LocationID*'] (currently always None - not implemented)
            # Fifth, match Location if exists TODO
            df_with_location_ids = FileParserService().parse_location(df=df_with_rate_type_ids, db_service=db_service)
            print("Fifth step done.")

            """
            Now we have all this data:
                - Date
                - CustomerID
                - CustomerReportedHours
                - FileID
                - EmployeeID
                - RateTypeID
                - Notes (optional, not implemented in any template yet, not necessary, no parsing necessary)
                - LocationID (optional but needs parsing, not implemented in any template yet) TODO

            This goes in the 'Match Section':
                - ProjectID
                - TaskID            # Where do we get the task name? this stays blank for now.
                - WorkOrderID
                - TimesheetEntryID  # We should match all the above at once with the data we already have(date, customer, employee) and assign the TimesheetEntryID
                                    # We could also try and match the errors from customer or employee match

            This gets assigned automatically on db insert:
                - CustomerReportedHoursID
            """

            # Returns: pd.DataFrame with added columns ['ProjectID*', 'WorkOrderID*', 'MultipleWorkOrderIDs*', 'ErrorMessage*']
            # Sixth, match work orders based on EmployeeID
            df_with_work_order_ids = FileParserService().parse_work_orders(df=df_with_location_ids, all_active_work_orders=all_active_work_orders, db_service=db_service)
            print("Sixth step done.")

            print("Dataframe with all IDs:")
            print(df_with_work_order_ids.columns)
            # print all rows, but just columns: Date, EmployeeName, CustomerName, ProjectID, WorkOrderID, CustomerReportedHours, RateType, ErrorMessage
            print(df_with_work_order_ids[['Date', 'EmployeeName', 'FileName']])
            # print(df_with_work_order_ids.dtypes)
            # print(df_with_work_order_ids.shape)
            # print(df_with_work_order_ids.describe())

            # Till this point we have IDed customers and employees for easier timesheet entry matching.
            # Match Section:
            """
            How match section should work:
            Should we convert the all_active_work_orders to a dataframe? - yes

            Match every dataframe row to an active work order where the employee id is the same, but if the employee has more than one active order, add an error message and ask user to select the correct one.
            If a row doesnt match any active work order, then add an error message saying 'No active work order found for this employee. Suggestions, fix name or check work order in odoo.'
            When a work order matches the employee id, and its just one, fill in the other fields (CustomerID, ProjectID, TaskID, WorkOrderID) for each row where the employee id matches.
                - Should we validate first the CustomerID?
                - Or should we remove CustomerID matching, and simply match by EmployeeID and then add the other fields (CustomerID, ProjectID, TaskID, WorkOrderID)?
            """

            # Final match try:
            # For the rows with errors, try matching based on full active work orders.
            # So, we have the active work orders, and we can filter by date (or week ending - wich might be better due to some customers not sending specific day hours), customer, and employee.

            # First try to match the active work order employees to a line of the customer timesheets dataframe
            
            # Validate and check blanks

            # Validation: (mainly hours validation)
            # validate hours:
            #     No more than 24h/day
            #         If so, check total week hours. If still more than 24h/day (120h), throw error. Else divide in hourtypes.
            # validate date:
            #   validate date between week starting and week ending

            # TODO: Store processed data in database
            '''
            Data expected to be saved in database:
                        Phases              P1  P2  P3  P4  P5  P6
                - CustomerReportedHoursID   -   -   -   -   -   -   ⌂   This is returned when saved on db
                - TimesheetEntryID          -   -   -   -   -   ⌂       Filled right before the db insert
                - Date                      -   ⌂
                - CustomerID                -   -   ⌂
                - LocationID                -   ⌂
                - ProjectID                 -   -   -   ⌂
                - TaskID                    -   -   -   -   ⌂ (tf this comes from? - it seems that this field never gets filled :( )
                - CustomerReportedHours     -   ⌂
                - Notes                     -   ⌂
                - FileID                    ⌂
                - RateTypeID                -   ⌂
                - WorkOrderID               -   -   -   ⌂
                - EmployeeID                -   -   ⌂
            '''

            # Should i add a new column called WarningMessages, to differentiate from errors?

            # Before sending df to frontend, ensure we have the following columns:
            # CustomerReportedHoursID, TimesheetEntryID, Date, CustomerID, LocationID, ProjectID, TaskID, CustomerReportedHours, Notes, FileID, RateTypeID, WorkOrderID, EmployeeID
            # and the added columns:
            # CustomerName, EmployeeName, ErrorMessage, PossibleCustomerIDs, PossibleEmployeeIDs, MultipleWorkOrderIDs
            # So lets check the df and add any missing columns
            required_columns = [
                # 'CustomerReportedHoursID', # Returned when saved on db
                # 'TimesheetEntryID', # Assigned on db insert
                'Date',
                'EmployeeID',
                'EmployeeName',
                'CustomerID',
                'CustomerName',
                'CustomerReportedHours',
                'RateTypeID',
                'RateType',
                'ProjectID',
                'ProjectName',
                'WorkOrderID',
                'WorkOrderNumber',
                'FileID',
                'FileName',
                'Notes',
                'TaskID',
                'LocationID',
                'ErrorMessage',
                'PossibleCustomerIDs',
                'PossibleEmployeeIDs',
                'MultipleWorkOrderIDs'
            ]
            missing_columns = [col for col in required_columns if col not in df_with_work_order_ids.columns]
            for col in missing_columns:
                df_with_work_order_ids[col] = None
                print(f"Added column {col} to dataframe")

            # Reorder columns
            df_with_work_order_ids = df_with_work_order_ids[required_columns]

        except Exception as e:
            print(f"Error processing files: {e}")
            airtanker_app.logger.exception("Error processing files and templates")
        finally:
            db_service.disconnect()

        # Store the processed dataframe in session for display
        if 'df_with_work_order_ids' in locals():
            # Convert dataframe to JSON for session storage
            session['processed_timesheet_data'] = df_with_work_order_ids.to_json(orient='records', date_format='iso')
            session['processed_timesheet_week_ending'] = selected_week_ending

            return jsonify({
                'redirect_url': url_for('timesheet_entries_display')
            })
        else:
            return jsonify({
                'redirect_url': url_for(
                    'upload_customer_timesheets_ai',
                    week_ending_date=selected_week_ending
                )
            })

    try:
        db_service = DatabaseService()
        db_service.connect()
        # Returns: [{'id', 'name', 'identifiers*', 'type', 'prompt', 'response_schema'}]
        templates = db_service.get_templates()

    except Exception as e:
        print(f"Failed to get templates: {e}")
        templates = []

    selected_week_ending = session.get('selected_week_ending')
    return render_template('timesheet_wizard/upload_customer_timesheets_ai.html', templates=templates, selected_week_ending=selected_week_ending)


# Display processed timesheet entries
@airtanker_app.route('/timesheet_entries_display', methods=['GET'])
@requires_odoo_authentication
@requires_authentication
def timesheet_entries_display():
    # Get processed data from session
    processed_data_json = session.get('processed_timesheet_data')
    week_ending = session.get('processed_timesheet_week_ending')

    if not processed_data_json:
        # No processed data, redirect back to upload
        return redirect(url_for('upload_customer_timesheets_ai'))

    # Convert JSON back to list of dictionaries for template
    import json
    processed_data = json.loads(processed_data_json)

    return render_template('timesheet_wizard/timesheet_entries_display.html',
                         entries=processed_data,
                         week_ending=week_ending)


# Export processed timesheet data
@airtanker_app.route('/export_processed_timesheet_data', methods=['GET'])
@requires_odoo_authentication
@requires_authentication
def export_processed_timesheet_data():
    # Get processed data from session
    processed_data_json = session.get('processed_timesheet_data')
    week_ending = session.get('processed_timesheet_week_ending')

    if not processed_data_json:
        return jsonify({'error': 'No processed data found in session'}), 404

    # Convert JSON back to DataFrame
    import json

    processed_data = json.loads(processed_data_json)
    df = pd.DataFrame(processed_data)

    # Create CSV output
    output = io.StringIO()
    df.to_csv(output, index=False)
    csv_data = output.getvalue()
    output.close()

    # Create response
    response = make_response(csv_data)
    response.headers['Content-Type'] = 'text/csv'
    response.headers['Content-Disposition'] = f'attachment; filename=processed_timesheet_data_{week_ending or "unknown_date"}.csv'

    return response


# API endpoint to get timesheet entries data for AJAX requests
@airtanker_app.route('/api/timesheet_entries_data', methods=['GET'])
@requires_odoo_authentication
@requires_authentication
def api_timesheet_entries_data():
    processed_data_json = session.get('processed_timesheet_data')

    if not processed_data_json:
        return jsonify({'error': 'No processed data found'}), 404

    import json
    processed_data = json.loads(processed_data_json)

    return jsonify({'data': processed_data})


# Test endpoint with sample data for development
@airtanker_app.route('/timesheet_entries_display_test', methods=['GET'])
@requires_odoo_authentication
@requires_authentication
def timesheet_entries_display_test():
    # Sample data for testing
    sample_data = [
        {
            'Date': '2025-06-16',
            'EmployeeName': 'Harpreet Dhesi',
            'CustomerName': 'ATS Corporation',
            'ProjectID': '21001',
            'WorkOrderID': '3001',
            'CustomerReportedHours': 7.91,
            'RateTypeID': 1,
            'Notes': '',
            'ErrorMessage': '',
            'CustomerID': 101,
            'EmployeeID': 10111
        },
        {
            'Date': '2025-06-16',
            'EmployeeName': 'Cody Vine',
            'CustomerName': 'AtomTech Inc.',
            'ProjectID': '21114',
            'WorkOrderID': '3002',
            'CustomerReportedHours': 10.0,
            'RateTypeID': 2,
            'Notes': '',
            'ErrorMessage': '',
            'CustomerID': 102,
            'EmployeeID': 10222
        },
        {
            'Date': '2025-06-17',
            'EmployeeName': 'Harpreet Dhesi',
            'CustomerName': 'ATS Corporation',
            'ProjectID': '21001',
            'WorkOrderID': '3001',
            'CustomerReportedHours': 8.8,
            'RateTypeID': 1,
            'Notes': '',
            'ErrorMessage': '',
            'CustomerID': 101,
            'EmployeeID': 10111
        },
        {
            'Date': '2025-06-17',
            'EmployeeName': 'Cody Vine',
            'CustomerName': 'AtomTech Inc.',
            'ProjectID': '21114',
            'WorkOrderID': '3002',
            'CustomerReportedHours': 10.0,
            'RateTypeID': 2,
            'Notes': 'Overtime approved',
            'ErrorMessage': '',
            'CustomerID': 102,
            'EmployeeID': 10222
        }
    ]

    return render_template('timesheet_wizard/timesheet_entries_display.html',
                         entries=sample_data,
                         week_ending='2025-06-21')


# Step 3.5 - Resolve Customer Timesheet Conflicts
@airtanker_app.route('/resolve_customer_errors', methods=['GET', 'POST'])
@requires_odoo_authentication
@requires_authentication
def resolve_customer_errors():
    if request.method in ('POST'):
        data_received = request.json
        push_updates_customer(data_received, session['username'])
        completion_html = render_template('timesheet_wizard/wizard_complete.html')

        # Return the HTML content in the JSON response
        return jsonify({'success': True, 'completion_html': completion_html})
    return render_template('timesheet_wizard/resolve_customer_errors.html')

#### STEP 4 - APPROVALS
@airtanker_app.route('/approvals/timesheets', methods=['GET', 'POST'])
#@requires_odoo_authentication
@requires_authentication
def approvals():
    form = WorkOrderForm()

    selected_week_ending = session.get('selected_week_ending')
    if not selected_week_ending:
        selected_week_ending = False

    return render_template('endpoints/timesheet_approvals.html', form=form, selected_week_ending=selected_week_ending)

@airtanker_app.route('/update_travel_entries', methods=['POST'])
@requires_odoo_authentication
@requires_authentication
def update_travel_entries():    
    if request.method in ('POST'):
        data_received = request.json
        if data_received:
            update_travel_hours_for_not_errored_employees(data_received, session['username'])
        return jsonify(status='success'), 200

@airtanker_app.route('/import_fixed_time', methods=['GET', 'POST'])
@requires_odoo_authentication
@requires_authentication
def import_fixed_time():
    return render_template('timesheet_wizard/upload_fixed_timesheets.html')
